import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { Box, CircularProgress, Typography } from '@mui/material';
import DesignerContainer from '../../Components/IDDesigner/DesignerContainer';
import { loadTemplate, markTemplateAsSaved, setLoading } from '../../redux/idDesignerSlice';
import { getBadgeTemplateById, getBadgeTemplates, getSchemas, getSchemaKeys } from '../../api/badge';
import {
  loadTemplateFromStorage,
  saveTemplateToStorage,
  isTemplateLocallyModified,
  markTemplateAsSynced
} from '../../utils/templateStorage';
import imageManager from '../../utils/imageManager';
import { migrateElementImages, needsImageMigration } from '../../utils/imageMigration';

const IDDesigner = () => {
  const { templateId } = useParams();
  const dispatch = useDispatch();
  const { loading, templateChanged, currentTemplate } = useSelector((state) => state.idDesigner);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    const loadTemplateData = async () => {
      if (templateId) {
        try {
          setInitialLoading(true);
          dispatch(setLoading(true));

          // Step 1: Load badge templates to get schema information
          console.log('Step 1: Loading badge templates...');
          const templatesResponse = await getBadgeTemplates();
          const templates = templatesResponse.data?.data || [];

          // Find the current template in the list to get its schema
          const currentTemplateInfo = templates.find(t => t.badge_id === templateId);
          const schemaName = currentTemplateInfo?.schema;

          console.log('Found template info:', currentTemplateInfo);
          console.log('Schema name:', schemaName);

          // Step 2: Load schemas
          console.log('Step 2: Loading schemas...');
          const schemasResponse = await getSchemas();
          const schemas = schemasResponse.data?.schemas || [];
          console.log('Available schemas:', schemas);

          // Step 3: Load schema keys if we have a schema name
          let schemaKeys = [];
          if (schemaName) {
            console.log('Step 3: Loading schema keys for schema:', schemaName);
            const keysResponse = await getSchemaKeys(schemaName);
            schemaKeys = keysResponse.data || [];
            console.log('Schema keys:', schemaKeys);
          }

          // Step 4: Load the actual template data
          console.log('Step 4: Loading template data...');

          // First check if we have local data
          const localData = loadTemplateFromStorage(templateId);

          // Fetch from API
          const response = await getBadgeTemplateById(templateId);
          const apiData = response.data;

          let templateToLoad = null;

          // Check if local data is more recent or if we should use API data
          if (localData && isTemplateLocallyModified(templateId, apiData.updated_at)) {
            // Use local data if it's more recent
            let elements = localData.content?.elements || localData.config?.elements || localData.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            templateToLoad = {
              id: templateId,
              name: localData.name,
              lastModified: localData.lastModified,
              canvasConfig: localData.content?.canvasConfig || localData.config?.canvasConfig || localData.canvasConfig || {},
              elements: elements,
              schema: localData.schema || apiData.schema,
              key: localData.key || apiData.key,
              format: localData.format || apiData.format,
              // Add the loaded schema data for reference
              availableSchemas: schemas,
              schemaKeys: schemaKeys
            };
            toast.info(`Loaded local version of "${localData.name}" (has unsaved changes)`);
          } else {
            // Use API data and save to local storage
            let elements = apiData.content?.elements || apiData.config?.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            templateToLoad = {
              id: apiData.badge_id,
              name: apiData.name,
              lastModified: apiData.updated_at || apiData.created_at,
              canvasConfig: apiData.content?.canvasConfig || apiData.config?.canvasConfig || {},
              elements: elements,
              schema: apiData.schema,
              key: apiData.key,
              format: apiData.format,
              // Add the loaded schema data for reference
              availableSchemas: schemas,
              schemaKeys: schemaKeys
            };

            // Save API data to local storage
            saveTemplateToStorage(templateId, {
              name: templateToLoad.name,
              content: {
                canvasConfig: templateToLoad.canvasConfig,
                elements: templateToLoad.elements
              },
              schema: templateToLoad.schema,
              key: templateToLoad.key,
              format: templateToLoad.format
            });

            // Mark as synced since we just loaded from API
            markTemplateAsSynced(templateId);

            toast.success(`Template "${templateToLoad.name}" loaded successfully`);
          }

          // Load the template into the designer
          dispatch(loadTemplate(templateToLoad));

          // Mark as saved if using API data
          if (!localData || !isTemplateLocallyModified(templateId, apiData.updated_at)) {
            dispatch(markTemplateAsSaved());
          }

        } catch (error) {
          console.error('Error loading template:', error);

          // Try to load from local storage as fallback
          const localData = loadTemplateFromStorage(templateId);
          if (localData) {
            let elements = localData.content?.elements || localData.config?.elements || localData.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            dispatch(loadTemplate({
              id: templateId,
              name: localData.name,
              lastModified: localData.lastModified,
              canvasConfig: localData.content?.canvasConfig || localData.config?.canvasConfig || localData.canvasConfig || {},
              elements: elements,
              schema: localData.schema,
              key: localData.key,
              format: localData.format,
              availableSchemas: [],
              schemaKeys: []
            }));
            toast.warning(`Loaded local version of "${localData.name}" (API unavailable)`);
          } else {
            toast.error('Failed to load template');
          }
        } finally {
          setInitialLoading(false);
          dispatch(setLoading(false));
        }
      }
    };

    loadTemplateData();
  }, [templateId, dispatch]);

  // Cleanup image manager when component unmounts
  useEffect(() => {
    return () => {
      imageManager.clear();
    };
  }, []);

  if (initialLoading) {
    return (
      <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            gap: 2
          }}
        >
          <CircularProgress size={60} sx={{ color: '#4F2683' }} />
          <Typography variant="h6" color="#4F2683">
            Loading Template...
          </Typography>
        </Box>
      </div>
    );
  }

  return (
    <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <h2 className="font-normal text-[24px] text-[#4F2683]">
          ID Card Designer{currentTemplate.name ? ` - ${currentTemplate.name}` : ''}
        </h2>
        {templateChanged && (
          <Typography
            variant="caption"
            sx={{
              backgroundColor: '#ff9800',
              color: 'white',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              fontWeight: 'bold'
            }}
          >
            UNSAVED
          </Typography>
        )}
      </Box>
      <div className="flex-1 overflow-hidden">
        <DesignerContainer />
      </div>
    </div>
  );
};

export default IDDesigner;
